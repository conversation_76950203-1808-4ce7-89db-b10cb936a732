<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <style>
        /* 报告头部背景样式 */
        .report-header-bg {
            position: relative;
            min-height: 280px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: local;
            margin: -16px -16px 25px -16px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0.05) 0%,
                rgba(0, 0, 0, 0.02) 30%,
                rgba(0, 0, 0, 0.1) 70%,
                rgba(0, 0, 0, 0.3) 100%
            );
        }

        .header-content {
            position: relative;
            z-index: 3;
            width: 100%;
        }

        .report-header-bg .report-title-section {
            text-align: center;
            padding: 15px 20px 25px 20px;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.05) 100%
            );
            backdrop-filter: blur(15px) saturate(1.2);
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            margin: 100px 0 0 0;
            position: relative;
        }

        .report-header-bg .report-subtitle {
            text-shadow:
                0 2px 4px rgba(0, 0, 0, 0.9),
                0 1px 2px rgba(0, 0, 0, 0.7),
                0 0 15px rgba(255, 255, 255, 0.4);
            color: #ffffff !important;
            font-weight: 500;
            font-size: 16px;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .report-header-bg .report-main-title {
            text-shadow:
                0 3px 6px rgba(0, 0, 0, 0.9),
                0 1px 3px rgba(0, 0, 0, 0.7),
                0 0 20px rgba(255, 255, 255, 0.4);
            color: #ffffff !important;
            font-weight: 700;
            letter-spacing: 2px;
            font-size: 28px;
            text-transform: uppercase;
            position: relative;
            margin-bottom: 0;
        }

        .report-header-bg .report-main-title::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #ffffff, transparent);
            border-radius: 2px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .report-header-bg {
                min-height: 240px;
                margin: -16px -16px 20px -16px;
            }

            .report-header-bg .report-title-section {
                padding: 12px 16px 20px 16px;
                margin: 80px 0 0 0;
            }

            .report-header-bg .report-main-title {
                font-size: 24px;
                letter-spacing: 1.5px;
            }

            .report-header-bg .report-subtitle {
                font-size: 15px;
            }
        }

        @media (max-width: 480px) {
            .report-header-bg {
                min-height: 220px;
                margin: -16px -16px 16px -16px;
            }

            .report-header-bg .report-title-section {
                padding: 10px 12px 18px 12px;
                margin: 60px 0 0 0;
            }

            .report-header-bg .report-main-title {
                font-size: 20px;
                letter-spacing: 1px;
            }

            .report-header-bg .report-subtitle {
                font-size: 14px;
            }

            .report-header-bg .report-main-title::after {
                width: 40px;
                height: 2px;
                bottom: -5px;
            }
        }

        /* 表格优化 */
        .table-responsive {
            -webkit-overflow-scrolling: touch;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table {
            margin-bottom: 0;
            background: white;
        }

        .table thead th {
            position: sticky;
            top: 0;
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            z-index: 10;
        }

        /* 图表容器样式优化 */
        .charts-container {
            overflow: hidden;
        }

        .chart-item {
            margin-bottom: 20px;
            overflow: visible;
            background: white;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 10px;
        }

        .chart-item > div {
            overflow: visible !important;
        }

        .chart-item canvas,
        .chart-item > div {
            max-width: 100%;
            overflow: visible;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            #container_1, #container_2, #complex-container {
                min-height: 250px;
                width: 100% !important;
                max-width: 100% !important;
                overflow: visible !important;
            }

            .chart-item canvas {
                max-width: 100% !important;
                width: 100% !important;
            }
        }

        @media (max-width: 480px) {
            #container_1, #container_2, #complex-container {
                min-height: 200px;
                width: 100% !important;
                max-width: 100% !important;
                overflow: visible !important;
            }

            .chart-item {
                padding: 5px;
                margin: 5px 0;
            }

            .chart-item canvas {
                max-width: 100% !important;
                width: 100% !important;
                height: auto !important;
            }
        }

        /* 滚动提示 */
        .scroll-hint {
            text-align: center;
            font-size: 12px;
            color: #666;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
        }

        /* 表格滚动指示器 */
        .table-responsive.scrollable {
            position: relative;
        }

        .table-responsive.scrollable::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 20px;
            background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
            pointer-events: none;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="pageTitle">
            《<span class="scaleName"></span>》测试报告
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule">
        <div class="alert hide text-center text-success" id="alert">
            <div class="splashBlock">
                <div class="mb-3 mt-3">
                    <img th:src="@{/static/images/app/success.png}" alt="draw" width="100">
                </div>
                <div class="sectionTitle text-center">
                    <div class="lead font16 font-weight-bold" id="msg">

                    </div>
                </div>
            </div>
        </div>
        <div class="appContent" id="report">
            <!-- 报告头部背景区域 -->
            <div class="report-header-bg" id="reportHeaderBg" style="display: none;">
                <div class="bg-overlay"></div>
                <div class="header-content">
                    <div class="report-title-section">
                        <div>
                            <p class="report-subtitle text-white">《<span class="scaleName"></span>》</p>
                            <h1 class="report-main-title text-white">心理测试报告</h1>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 普通报告头部 -->
            <div class="report-header" id="normalHeader">
                <div class="report-title-section">
                    <div>
                        <p class="report-subtitle">《<span class="scaleName"></span>》</p>
                        <h1 class="report-main-title">心理测试报告</h1>
                    </div>
                </div>
            </div>

            <!-- 基本信息卡片 -->
            <div class="report-section">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fa fa-info-circle"></i>
                    </div>
                    <h2 class="section-title">基本信息</h2>
                </div>
                <div class="info-grid pt-0">
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">用户名</span>
                        <span class="info-value" id="overviewName"></span>
                    </div>
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">所属组织</span>
                        <span class="info-value" id="overviewOrg"></span>
                    </div>
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">测试耗时</span>
                        <span class="info-value" id="overviewTime"></span>
                    </div>
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">测试日期</span>
                        <span class="info-value" id="overviewDate"></span>
                    </div>
                </div>
            </div>

            <div class="report-section hide" id="validFactorSection">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fa fa-check-circle"></i>
                    </div>
                    <h2 class="section-title">效度因子得分情况</h2>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th class="text-center">因子</th>
                            <th class="text-center">得分</th>
                            <th class="text-center">结果与建议</th>
                        </tr>
                        </thead>
                        <tbody id="validFactorTableBody">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 复合因子数据分析区域 -->
            <div class="report-section hide" id="complexFactorSection">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fa fa-chart-bar"></i>
                    </div>
                    <h2 class="section-title">复合因子得分情况</h2>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th class="text-center">因子</th>
                            <th class="text-center">因子分</th>
                            <th class="text-center">总分范围</th>
                        </tr>
                        </thead>
                        <tbody id="complexFactorTableBody">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 复合因子可视化图表区域 -->
            <div class="report-section hide" id="complexFactorChartsSection">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fa fa-chart-bar"></i>
                    </div>
                    <h2 class="section-title">复合因子数据可视化</h2>
                </div>
                <div class="charts-container">
                    <div class="chart-item hide" id="divComplexChart1">
                        <h4>复合因子分析图</h4>
                        <canvas id="complexContainer1" style="height:400px; margin:0 auto;" class="pt-2 hide"></canvas>
                        <div id="complexContainer_1" style="min-width: 300px; min-height: 350px; width: 100%; max-width: 100%; overflow: visible;"></div>
                    </div>

                    <div class="chart-item hide" id="divComplexChart2">
                        <h4>复合因子统计图</h4>
                        <canvas id="complexContainer2" style="height:400px; margin:0 auto;" class="pt-2 hide"></canvas>
                        <div id="complexContainer_2" style="min-width: 300px; min-height: 350px; width: 100%; max-width: 100%; overflow: visible;"></div>
                    </div>
                </div>
            </div>

            <!-- 复合因子结果解释区域 -->
            <div class="report-section hide" id="complexFactorExplainSection">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fa fa-graduation-cap"></i>
                    </div>
                    <h2 class="section-title">复合因子结果解释</h2>
                </div>
                <div class="interpretation-content" id="complexFactorExplain">
                    <dl>
                    </dl>
                </div>
            </div>

            <!-- 普通因子数据分析区域 -->
            <div class="report-section" id="singleFactorSection">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fa fa-chart-line"></i>
                    </div>
                    <h2 class="section-title">因子得分情况</h2>
                </div>
                <div class="table-responsive">
                    <th:block th:if="${testRecord.scale.id} == ${sds}">
                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th class="text-center"></th>
                                <th class="text-center">原始分</th>
                                <th class="text-center">标准分</th>
                                <th class="text-center">抑郁指数</th>
                                <th class="text-center">划界标准</th>
                            </tr>
                            </thead>
                            <tbody id="singleFactorTableBody">
                            </tbody>
                        </table>
                    </th:block>
                    <th:block th:unless="${testRecord.scale.id} == ${sds}">
                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th class="text-center">因子</th>
                                <th class="text-center">原始分</th>
                                <th class="text-center">因子分</th>
                                <th class="text-center">总分范围</th>
                            </tr>
                            </thead>
                            <tbody id="singleFactorTableBody">
                            </tbody>
                        </table>
                    </th:block>
                </div>
            </div>

            <!-- 普通因子可视化图表区域 -->
            <div class="report-section" id="singleFactorChartsSection">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fa fa-pie-chart"></i>
                    </div>
                    <h2 class="section-title">因子数据可视化</h2>
                </div>
                <div class="charts-container">
                    <div class="chart-item hide" id="divSingleChart1">
                        <h4>因子分析图</h4>
                        <canvas id="singleContainer1" style="height: 400px; min-width: 400px; margin:0 auto; " class="pt-2 hide"></canvas>
                        <div id="singleContainer_1" style="min-width: 300px; min-height: 300px; width: 100%; max-width: 100%; overflow: visible;"></div>
                    </div>

                    <div class="chart-item hide" id="divSingleChart2">
                        <h4>因子统计图</h4>
                        <canvas id="singleContainer2" style="height: 400px; min-width: 400px; margin:0 auto;" class="pt-2 hide"></canvas>
                        <div id="singleContainer_2" style="min-width: 300px; min-height: 300px; width: 100%; max-width: 100%; overflow: visible;"></div>
                    </div>
                </div>
            </div>

            <!-- 普通因子结果解释区域 -->
            <div class="report-section" id="singleFactorExplainSection">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fa fa-graduation-cap"></i>
                    </div>
                    <h2 class="section-title">因子结果解释</h2>
                </div>
                <div class="interpretation-content" id="singleFactorExplain">
                    <dl>
                    </dl>
                </div>
            </div>


            <!-- 额外的底部间距，确保内容不被遮挡 -->
            <div class="report-footer-spacer"></div>
        </div>
    </div>

    <th:block th:insert="~{layouts/footMeasuring}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script th:src="@{/static/js/pages/chartDataConfig.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let record, scale, user, scaleName;
        let factors = [], complexFactors = [],complexScores=[],originalFactors = [], scores = [], standartScores = [],chartsImgArray = [];
        let isComplex = 0;
        let factorExplainsData = []; // 存储因子解释数据

        let initReport = function () {
            layer.open({type: 2, content: '报告加载中…', shadeClose: false});
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                dataType: "json",
                contentType: "application/json",
                async: false,
                success: function (data) {
                    layer.closeAll();
                    let res = JSON.parse(data);
                    if (res.resultCode !== undefined) {
                        if (res.resultCode === 201) {
                            $("#report").hide();
                            $(".appHeader").hide();
                            $("#msg").append("您已经完成测试！");
                            $("#alert").removeClass('hide').addClass('show');
                        }
                    }
                    responseData = res.data;
                    record = res.data[0].testRecord;
                    scale =  res.data[0].testRecord.scale;
                    user =  res.data[0].testRecord.user;

                    // 数据加载完成后初始化页面
                    //基本信息
                    getBaseInfo();
                    if (scale.id === [[${sds}]])//抑郁自评量表
                        sdsReport(responseData);
                    else
                        generalReport(responseData);

                    //获取因子结果解释
                    getFactorExplains(recordId);
                    if (getUrlParam("savecharts") === 'true') {
                        saveCharts();
                    }
                }
            });
        };
        let getBaseInfo = function () {
            $(".scaleName").html(scale.scaleName);
            $("#realName").html(user.realName === "" ? user.loginName : user.realName);
            $("#fullStructName").html(user.structFullName);
            $("#startDate").html(moment(record.startTime).format("YYYY-MM-DD"));
            $("#costTime").html(formatSeconds(record.timeInterval));

            // 填充新UI的数据
            $("#statusDate").html(moment(record.startTime).format("YYYY-MM-DD"));
            $("#heroName").html(user.realName === "" ? user.loginName : user.realName);
            $("#heroCostTime").html(formatSeconds(record.timeInterval));
            $("#overviewName").html(user.realName === "" ? user.loginName : user.realName);
            $("#overviewOrg").html(user.structFullName);
            $("#overviewTime").html(formatSeconds(record.timeInterval));
            $("#overviewDate").html(moment(record.startTime).format("YYYY-MM-DD"));
        };

        // 获取因子结果解释
        let getFactorExplains = function(recordId) {
            $.getJSON('/measuringroom/testing/getFactorExplains?recordId=' + recordId, function(result) {
                if(result.resultCode === 200 && result.data && result.data.length > 0) {
                    let complexFactorExplains = "";
                    let singleFactorExplains = "";
                    let hasComplexExplain = false;
                    let hasSingleExplain = false;
                    let hasValidExplain = false;
                    factorExplainsData = result.data;
                    // 根据因子类型分组解释
                    $.each(result.data, function(index, explain) {
                        if(explain.factorId === 0) {
                            // factorId为0的是总体解释，已移除总体解释区域，跳过
                            return true;
                        }

                        // 根据factorId查找对应的因子信息
                        let factorInfo = null;
                        $.each(responseData, function(i, data) {
                            if(data.factor.id === explain.factorId) {
                                factorInfo = data.factor;
                                return false;
                            }
                        });

                        if(factorInfo) {
                            if(factorInfo.isLie === 1) {
                                // 效度因子解释 - 更新效度因子表格
                                hasValidExplain = true;
                                updateValidFactorTable();
                            }
                            else if(factorInfo.factorType === 2) {
                                // 复合因子解释
                                hasComplexExplain = true;
                                complexFactorExplains += "<dt class='pb10'>" + factorInfo.factorName + "</dt>";
                                complexFactorExplains += "<dd class='pb10'>" + explain.interpretation + "</dd>";
                            } else {
                                // 普通因子解释
                                hasSingleExplain = true;
                                singleFactorExplains += "<dt class='pb10'>" + factorInfo.factorName + "</dt>";
                                singleFactorExplains += "<dd class='pb10'>" + explain.interpretation + "</dd>";
                            }
                        }
                    });

                    // 显示复合因子解释
                    if(hasComplexExplain) {
                        $("#complexFactorExplain dl").html(complexFactorExplains);
                        $("#complexFactorExplainSection").removeClass('hide');
                    } else {
                        $("#complexFactorExplainSection").addClass('hide');
                    }

                    // 显示普通因子解释
                    if(hasSingleExplain) {
                        $("#singleFactorExplain dl").html(singleFactorExplains);
                        $("#singleFactorExplainSection").removeClass('hide');
                    } else {
                        $("#singleFactorExplainSection").addClass('hide');
                    }
                }
            })
        };
        let getFactorInfo = function (res) {
            factors.splice(0);
            complexFactors.splice(0);
            complexScores.splice(0);
            originalFactors.splice(0);
            scores.splice(0);
            standartScores.splice(0);
            let singleFactorRows = "";
            let complexFactorRows = "";
            let hasSingleFactor = false;
            let hasComplexFactor = false;
            let validFactorRows = "";
            let hasValidFactor = false;

            $.each(res, function (index, content) {
                let factor = content.factor;
                // 检查是否为效度因子（测谎量表）
                if(factor.isLie === 1) {
                    hasValidFactor = true;
                    let explanation = getFactorExplanation(factor.id);
                    validFactorRows += "<tr><td class='text-center'>" + factor.factorName + "</td><td class='text-center'>" + toDecimal(content.score) + "</td><td class='text-center'>" + explanation + "</td></tr>";
                }
                if(factor.factorType === 1 && factor.isLie !== 1) {
                    // 普通因子
                    hasSingleFactor = true;
                    singleFactorRows += "<tr><td class='text-center'>" + factor.factorName + "</td><td class='text-center'>" + toDecimal(content.originalScore) + "</td><td class='text-center'>" + toDecimal(content.score) + "</td><td class='text-center'>"+factor.minScore+"~"+factor.maxScore+"</td></tr>";
                    if ((res.length > 1 && factor.factorName !== '总分') || res.length === 1) {
                        factors.push(factor.factorName);
                        originalFactors.push(content.originalScore);
                        standartScores.push(content.abnormalValue);
                        scores.push(content.score);
                    }
                } else if(factor.factorType === 2 && factor.isLie !== 1) {
                    // 复合因子
                    hasComplexFactor = true;
                    isComplex = 1;
                    complexFactorRows += "<tr><td class='text-center'>" + factor.factorName + "</td><td class='text-center'>" + toDecimal(content.score) + "</td><td class='text-center'>"+factor.minScore+"~"+factor.maxScore+"</td></tr>";
                    if (res.length > 1 && factor.factorName !== '总分') {
                        complexFactors.push(factor.factorName);
                        complexScores.push(content.score);
                    }
                }
            });
            if (scale.id == [[${apmAndNineHouse}]]) {
                factors.shift();
                scores.shift();
                standartScores.shift();
            }
            // 填充效度因子表格
            if(hasValidFactor) {
                $("#validFactorTableBody").append(validFactorRows);
                $("#validFactorSection").removeClass('hide');
            } else {
                $("#validFactorSection").addClass('hide');
            }
            // 填充普通因子表格
            if(hasSingleFactor) {
                $("#singleFactorTableBody").append(singleFactorRows);
                $("#singleFactorSection").removeClass('hide');
            } else {
                $("#singleFactorSection").addClass('hide');
            }

            // 填充复合因子表格
            if(hasComplexFactor) {
                $("#complexFactorTableBody").append(complexFactorRows);
                $("#complexFactorSection").removeClass('hide');
            } else {
                $("#complexFactorSection").addClass('hide');
            }
        };
        let generalReport = function (res) {
            if (scale.id == [[${pcm}]]) {
                $("#singleFactorSection").hide();
                $("#complexFactorSection").hide();
            }
            //因子得分情况
            getFactorInfo(res);
            creatCharts();
        };
        let sdsReport = function (res) {
            let listFactors = "";
            let originalScore = toDecimal(res[0].originalScore);
            let standardScore = toDecimal(res[0].score);
            let indexScore = toDecimal(res[0].originalScore / 80);
            let abnormalValue = toDecimal(res[0].abnormalValue);
            listFactors += "<tr>"
            listFactors += "<td class='text-center'>" + res[0].factor.factorName + "</td>";
            listFactors += "<td class='text-center'>" + originalScore + "</td>";
            listFactors += "<td class='text-center'>" + standardScore + "</td>";
            listFactors += "<td class='text-center'>" + indexScore + "</td>";
            listFactors += "<td class='text-center'>" + abnormalValue + "</td></tr>";
            $("#singleFactorTableBody").append(listFactors);
            $("#singleFactorSection").removeClass('hide');
            $("#complexFactorSection").addClass('hide');
            factors.push(res[0].factor.factorName);
            creatCharts();
        };
        let creatCharts = function(){
            // 分别处理普通因子和复合因子的图表
            createSingleFactorCharts();
            createComplexFactorCharts();
        };

        let createSingleFactorCharts = function() {
            // 获取普通因子图表配置
            let singleFactorCharts = scale.listCharts.filter(chart => chart.factorType === 1);

            // 如果没有配置普通因子图表，检查是否有旧的配置（没有factorType字段）
            if(singleFactorCharts.length === 0 && factors.length > 0) {
                // 检查是否有旧的图表配置（没有factorType字段的）
                let oldCharts = scale.listCharts.filter(chart => chart.factorType === undefined || chart.factorType === null);
                if(oldCharts.length > 0) {
                    // 使用旧的配置作为普通因子配置
                    singleFactorCharts = oldCharts.map(chart => ({...chart, factorType: 1}));
                } else {
                    // 使用默认配置
                    if(factors.length === 1) {
                        singleFactorCharts.push({factorType: 1, chartType: 'solidgauge'});
                    } else {
                        singleFactorCharts.push({factorType: 1, chartType: 'line'});
                        singleFactorCharts.push({factorType: 1, chartType: 'column'});
                    }
                }
            }

            $.each(singleFactorCharts, function(index, chartConfig) {
                // 深拷贝图表配置，避免数据污染
                let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));

                //抑郁自评量表特殊处理
                if (scale.id === [[${sds}]]){
                    chartOptions.series.push({ name: '抑郁指数值', "data": [responseData[0].originalScore / 80] });
                    chartOptions.title.text = '';
                    if(chartOptions.chart.type === 'gauge'){
                        chartOptions.yAxis.plotBands.push({from: 0, to: 0.49, color: '#55BF3B'});
                        chartOptions.yAxis.plotBands.push({from: 0.49, to: 0.6, color: '#DDDF0D'});
                        chartOptions.yAxis.plotBands.push({from: 0.6, to: 1, color: '#DF5353'});
                    }
                }
                else{
                    if(chartOptions.chart.type === 'gauge' || chartOptions.chart.type === 'solidgauge'){
                        // 对于速度仪图，显示第一个普通因子的数据
                        let singleFactorData = responseData.find(item => item.factor.factorType === 1);
                        if(singleFactorData) {
                            chartOptions.title.text = '';
                            chartOptions.yAxis.min = singleFactorData.factor.minScore;
                            chartOptions.yAxis.max = singleFactorData.factor.maxScore;
                            chartOptions.series.push({name: singleFactorData.factor.factorName, "data": [singleFactorData.score] });
                        }
                    }
                    else if(chartOptions.chart.type === 'pie') {
                        chartOptions.title.text = '';
                        let yData = [];
                        $.each(responseData.filter(item => item.factor.factorType === 1), function (index, content) {
                            yData.push({
                                name: content.factor.factorName, y: parseFloat((content.originalScore/90).toFixed(2))
                            })
                        });
                        chartOptions.series.push({
                            name: '因子分',
                            colorByPoint: true,
                            data:yData
                        });
                    }
                    else{
                        chartOptions.title.text = '';
                        chartOptions.xAxis.categories = factors;
                        chartOptions.series.push({ name: '因子分', "data": scores,color: "#ffbc00" });
                        chartOptions.series.push({ name: '异常界值', "data": standartScores,color: "#fa5c7c" });
                    }
                }

                let canvasId = "#singleContainer" + (index + 1);
                let divChart = "#divSingleChart" + (index + 1);
                $(divChart).removeClass('hide');
                $("#singleFactorChartsSection").removeClass('hide');
                chartOptions.chart.renderTo = 'singleContainer'+(index + 1);
                let chart1 = new Highcharts.Chart(chartOptions);
                let chartsOptions2 = JSON.parse(JSON.stringify(chartOptions));
                chartsOptions2.chart.renderTo = 'singleContainer_'+(index + 1);
                let chart2 = new Highcharts.Chart(chartsOptions2);
                let charData = $(canvasId).highcharts().getSVG();
                canvg('singleContainer'+(index + 1), charData);
                let chartsImg = $(canvasId)[0].toDataURL("image/png");
                chartsImgArray.push(chartsImg);
            });
        };

        let createComplexFactorCharts = function() {
            if(isComplex !== 1 || complexFactors.length === 0) {
                return;
            }

            // 获取复合因子图表配置
            let complexFactorCharts = scale.listCharts.filter(chart => chart.factorType === 2);

            // 如果没有配置复合因子图表，使用默认配置
            if(complexFactorCharts.length === 0) {
                complexFactorCharts.push({factorType: 2, chartType: 'bar'});
            }

            $.each(complexFactorCharts, function(index, chartConfig) {
                // 深拷贝图表配置，避免数据污染
                let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));

                if(chartOptions.chart.type === 'pie') {
                    chartOptions.title.text = '';
                    let yData = [];
                    $.each(responseData.filter(item => item.factor.factorType === 2), function (index, content) {
                        yData.push({
                            name: content.factor.factorName, y: parseFloat((content.score/100).toFixed(2))
                        })
                    });
                    chartOptions.series.push({
                        name: '因子分',
                        colorByPoint: true,
                        data:yData
                    });
                } else {
                    chartOptions.title.text = '';
                    chartOptions.xAxis.categories = complexFactors;
                    chartOptions.series.push({ name: '复合因子分', "data": complexScores, color: "#28a745"});
                }

                let canvasId = "#complexContainer" + (index + 1);
                let divChart = "#divComplexChart" + (index + 1);
                $(divChart).removeClass('hide');
                $("#complexFactorChartsSection").removeClass('hide');
                chartOptions.chart.renderTo = 'complexContainer'+(index + 1);
                let chart1 = new Highcharts.Chart(chartOptions);
                let chartsOptions2 = JSON.parse(JSON.stringify(chartOptions));
                chartsOptions2.chart.renderTo = 'complexContainer_'+(index + 1);
                let chart2 = new Highcharts.Chart(chartsOptions2);
                let charData = $(canvasId).highcharts().getSVG();
                canvg('complexContainer'+(index + 1), charData);
                let chartsImg = $(canvasId)[0].toDataURL("image/png");
                chartsImgArray.push(chartsImg);
            });
        };
        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.chartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {

                }
            });
        };
        // 根据因子ID获取解释内容
        let getFactorExplanation = function(factorId) {
            let explanation = factorExplainsData.find(explain => explain.factorId === factorId);
            return explanation ? explanation.interpretation : "";
        };

        // 更新效度因子表格
        let updateValidFactorTable = function() {
            let validFactorRows = "";
            let hasValidFactor = false;

            $.each(responseData, function (index, content) {
                let factor = content.factor;
                if(factor.isLie === 1) {
                    hasValidFactor = true;
                    let explanation = getFactorExplanation(factor.id);
                    validFactorRows += "<tr><td class='text-center'>" + factor.factorName + "</td><td class='text-center'>" + toDecimal(content.score) + "</td><td class='text-center'>" + explanation + "</td></tr>";
                }
            });

            if(hasValidFactor) {
                $("#validFactorTableBody").html(validFactorRows); // 使用html()替换内容
                $("#validFactorSection").removeClass('hide');
            }
        };
        $(function () {
            initReport();
            //检查并设置背景图片
            checkAndSetBackground();

            // 移动端优化
            if (window.innerWidth <= 768) {
                // 禁用双击缩放
                let lastTouchEnd = 0;
                document.addEventListener('touchend', function (event) {
                    let now = (new Date()).getTime();
                    if (now - lastTouchEnd <= 300) {
                        event.preventDefault();
                    }
                    lastTouchEnd = now;
                }, false);

                // 优化滚动性能
                document.addEventListener('touchmove', function(e) {
                    if (e.target.closest('.table-responsive')) {
                        // 允许表格滚动
                        return;
                    }
                }, { passive: true });

                // 图表容器触摸优化
                $('.chart-item').on('touchstart', function() {
                    $(this).addClass('touching');
                }).on('touchend', function() {
                    $(this).removeClass('touching');
                });

                // 移动端图表响应式处理
                setTimeout(function() {
                    if (typeof Highcharts !== 'undefined') {
                        Highcharts.charts.forEach(function(chart) {
                            if (chart && (chart.options.chart.type === 'gauge' || chart.options.chart.type === 'solidgauge')) {
                                // 调整gauge图表的spacing和size
                                chart.update({
                                    chart: {
                                        spacing: [5, 5, 5, 5]
                                    },
                                    pane: {
                                        size: window.innerWidth <= 480 ? '80%' : '90%',
                                        center: window.innerWidth <= 480 ? ['50%', '60%'] : ['50%', '65%']
                                    }
                                });
                            }
                        });
                    }
                }, 1000);
            }

            // 表格横向滚动提示
            $('.table-responsive').each(function() {
                let $this = $(this);
                let $table = $this.find('.table');

                if ($table.width() > $this.width()) {
                    $this.addClass('scrollable');

                    // 添加滚动提示
                    if (window.innerWidth <= 768) {
                        $this.before('<div class="scroll-hint">← 左右滑动查看更多 →</div>');
                    }
                }
            });

            // 图表自适应
            $(window).on('resize orientationchange', function() {
                setTimeout(function() {
                    // 重新渲染图表以适应新尺寸
                    if (typeof Highcharts !== 'undefined') {
                        Highcharts.charts.forEach(function(chart) {
                            if (chart) {
                                chart.reflow();
                            }
                        });
                    }
                }, 300);
            });
        });

        // 检查并设置背景图片
        function checkAndSetBackground() {
            $.ajax({
                url: "/measuringroom/testing/get_background",
                type: 'POST',
                data: { recordId: recordId },
                dataType: "JSON",
                success: function (res) {
                    if (res.resultCode === 200 && res.data && res.data.showBackground && res.data.backgroundUrl) {
                        // 显示背景图片版本
                        $("#reportHeaderBg").css("background-image", "url('" + res.data.backgroundUrl + "')").show();
                        $("#normalHeader").hide();
                    } else {
                        // 显示普通版本
                        $("#reportHeaderBg").hide();
                        $("#normalHeader").show();
                    }
                },
                error: function () {
                    // 出错时显示普通版本
                    $("#reportHeaderBg").hide();
                    $("#normalHeader").show();
                }
            });
        }
    </script>
</th:block>
</body>
</html>