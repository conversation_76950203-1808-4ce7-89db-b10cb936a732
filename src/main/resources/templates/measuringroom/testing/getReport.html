<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .hide {
            display: none !important;
        }

        /* 报告头部背景样式 */
        .report-header-bg {
            position: relative;
            min-height: 300px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: local;
            margin: -20px -20px 30px -20px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0.1) 0%,
                rgba(0, 0, 0, 0.05) 30%,
                rgba(0, 0, 0, 0.2) 70%,
                rgba(0, 0, 0, 0.6) 100%
            );
        }

        .header-content {
            position: relative;
            z-index: 3;
            text-align: center;
            padding: 20px 40px 35px 40px;
            width: 100%;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.05) 100%
            );
            backdrop-filter: blur(15px) saturate(1.2);
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            margin-top: 100px;
        }

        .header-content h4 {
            text-shadow:
                0 3px 6px rgba(0, 0, 0, 0.9),
                0 1px 3px rgba(0, 0, 0, 0.7),
                0 0 20px rgba(255, 255, 255, 0.4);
            font-size: 32px;
            margin-bottom: 0;
            color: #ffffff !important;
            font-weight: 700;
            letter-spacing: 2px;
            text-transform: uppercase;
            position: relative;
        }

        .header-content h4::before {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #ffffff, transparent);
            border-radius: 2px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .report-header-bg {
                min-height: 230px;
                margin: -15px -15px 20px -15px;
            }

            .header-content {
                padding: 18px 20px 30px 20px;
                margin-top: 80px;
            }

            .header-content h4 {
                font-size: 24px;
                letter-spacing: 1.5px;
            }
        }

        @media (max-width: 480px) {
            .report-header-bg {
                min-height: 200px;
                margin: -10px -10px 15px -10px;
            }

            .header-content {
                padding: 15px 15px 25px 15px;
                margin-top: 60px;
            }

            .header-content h4 {
                font-size: 20px;
                letter-spacing: 1px;
            }

            .header-content h4::before {
                width: 40px;
                height: 2px;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row d-print-none">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理测试</a></li>
                    </ol>
                </div>
                <h4 class="page-title">心理测试</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card pt-2">
                <div class="card-body ml-4 mr-4" id="report">
                    <div class="d-print-none">
                        <div class="dropdown card-widgets">
                            <a href="#" class="dropdown-toggle arrow-none" data-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-ellipsis-v"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right">
                                <a href="javascript:window.print()" class="dropdown-item"><i class="fa fa-print mr-2"></i>打印</a>
                                <a href="javascript:download()" class="dropdown-item"><i class="fa fa-download mr-2"></i>下载报告[word]</a>
                            </div>
                        </div>
                    </div>
                    <!-- 报告头部背景区域 -->
                    <div class="report-header-bg" id="reportHeaderBg" style="display: none;">
                        <div class="bg-overlay"></div>
                        <div class="header-content">
                            <div class="text-center">
                                <h4 class="m-0 letter-spacing-2 font-weight-500 text-white">《<span class="scaleName"></span>》测评报告</h4>
                            </div>
                        </div>
                    </div>

                    <!-- 普通标题区域 -->
                    <div class="clearfix" id="normalHeader">
                        <div class="text-center mt-4 mb-4">
                            <h4 class="m-0 letter-spacing-2 font-weight-500">《<span class="scaleName"></span>》测评报告</h4>
                        </div>
                    </div>
                    <div class="row col-12">
                        <h4 class="mb-3">基本信息</h4>
                        <div class="row col-12">
                            <div class="col-4">
                                <p class="mb-2"><strong class="mr-2">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</strong><span id="realName"></span></p>
                                <p class="mb-2"><strong class="mr-2">测试日期：</strong><span id="startDate"></span></p>
                            </div>
                            <div class="col-4">
                                <p class="mb-2"><strong class="mr-2">所属组织：</strong><span id="fullStructName"></span></p>
                                <p class="mb-2"><strong class="mr-2">耗&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;时：</strong> <span id="costTime"></span></p>
                            </div>
                            <div class="col-4">
                                <p class="mb-2"><strong class="mr-2">测试项目：</strong><span class="scaleName"></span></p>
                                <p class="mb-2"></p>
                            </div>
                        </div>
                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <!-- end row -->
                    <div class="row col-12">
                        <h4 class="mb-3">结果分析</h4>
                        <div id="validFactorList" class="row col-12 hide">
                            <div class="table-responsive">
                                <table class="table table-centered table-bordered">
                                    <thead class="thead-light">
                                    <tr>
                                        <th class="text-center">因子</th>
                                        <th class="text-center">结果与建议</th>
                                    </tr>
                                    </thead>
                                    <tbody id="validFactorTableBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="divider dashed large mt-2 mb-2"></div>
                        <!-- 复合因子得分情况 -->
                        <div id="complexFactorList" class="row col-12 hide">
                            <h4 class="mb-3">复合因子得分情况</h4>
                            <div class="table-responsive">
                                <table class="table table-centered table-bordered">
                                    <thead class="thead-light">
                                    <tr>
                                        <th class="text-center">因子</th>
                                        <th class="text-center">得分</th>
                                        <th class="text-center">结果解释</th>
                                    </tr>
                                    </thead>
                                    <tbody id="complexFactorTableBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 复合因子图表展示 -->
                        <div class="row col-12 mt-3" id="complexFactorCharts">
                            <div class="col-12 hide" id="divComplexChart1">
                                <div class="card shadow-none">
                                    <h5 class="card-header">复合因子图表分析</h5>
                                    <canvas id="complexContainer1" style="height: 400px; min-width: 400px; margin:0 auto;" class="pt-2 hide"></canvas>
                                </div>
                            </div>
                            <div class="col-12 hide" id="divComplexChart2">
                                <div class="card shadow-none">
                                    <canvas id="complexContainer2" style="height: 400px; min-width: 400px; margin:0 auto;" class="pt-2 hide"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 复合因子结果解释 -->
                        <div class="row hide" id="complexFactorExplainSection">
                            <div class="col-12">
                                <h4 class="mb-1">复合因子结果解释</h4>
                                <div class="row mt-2">
                                    <div class="col-12" id="complexFactorExplain">
                                        <dl></dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="divider dashed large mt-2 mb-2"></div>

                        <!-- 普通因子得分情况 -->
                        <div id="singleFactorList" class="row col-12">
                            <h4 class="mb-3">得分情况</h4>
                            <div class="table-responsive">
                                <th:block th:if="${testRecord.scale.id} == ${sds}">
                                    <table class="table table-centered table-bordered">
                                        <thead class="thead-light">
                                        <tr>
                                            <th class="text-center"></th>
                                            <th class="text-center">原始分</th>
                                            <th class="text-center">标准分</th>
                                            <th class="text-center">抑郁指数</th>
                                            <th class="text-center">划界标准</th>
                                        </tr>
                                        </thead>
                                        <tbody id="singleFactorTableBody">
                                        </tbody>
                                    </table>
                                </th:block>
                                <th:block th:unless="${testRecord.scale.id} == ${sds}">
                                    <table class="table table-centered table-bordered">
                                        <thead class="thead-light">
                                        <tr>
                                            <th class="text-center">因子</th>
                                            <th class="text-center">原始分</th>
                                            <th class="text-center">因子分</th>
                                            <th class="text-center">总分范围</th>
                                        </tr>
                                        </thead>
                                        <tbody id="singleFactorTableBody">
                                        </tbody>
                                    </table>
                                </th:block>
                            </div>
                        </div>

                        <!-- 普通因子图表展示 -->
                        <div class="row col-12 mt-3" id="singleFactorCharts">
                            <div class="col-12 hide" id="divSingleChart1">
                                <div class="card shadow-none">
                                    <h5 class="card-header">因子图表分析</h5>
                                    <canvas id="singleContainer1" style="height: 400px; min-width: 400px; margin:0 auto; " class="pt-2 hide"></canvas>
                                </div>
                            </div>
                            <div class="col-12 hide" id="divSingleChart2">
                                <div class="card shadow-none">
                                    <canvas id="singleContainer2" style="height: 400px; min-width: 400px; margin:0 auto;" class="pt-2 hide"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 普通因子结果解释 -->
                        <div class="row" id="singleFactorExplainSection">
                            <div class="col-12">
                                <h4 class="mb-1">因子结果解释</h4>
                                <div class="row mt-2">
                                    <div class="col-12" id="singleFactorExplain">
                                        <dl></dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div> <!-- end card -->
            <div class="alert alert-light m-2 hide" role="alert">

            </div>
        </div> <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script th:src="@{/static/js/pages/chartDataConfig.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let record, scale, user, responseData;
        let factors = [], complexFactors = [],complexScores=[], originalFactors = [], scores = [], standartScores = [],chartsImgArray = [];
        let isComplex = 0;
        let factorExplainsData = []; // 存储因子解释数据

        // 根据因子ID获取解释内容
        let getFactorExplanation = function(factorId) {
            let explanation = factorExplainsData.find(explain => explain.factorId === factorId);
            return explanation ? explanation.interpretation : "用于检验答题的真实性和有效性";
        };

        // 更新效度因子表格
        let updateValidFactorTable = function() {
            let validFactorRows = "";
            let hasValidFactor = false;

            $.each(responseData, function (index, content) {
                let factor = content.factor;
                if(factor.isLie === 1) {
                    hasValidFactor = true;
                    let explanation = getFactorExplanation(factor.id);
                    validFactorRows += "<tr><td class='text-center'>" + factor.factorName + "</td><td class='text-center'>" + toDecimal(content.score) + "</td><td class='text-center'>" + explanation + "</td></tr>";
                }
            });

            if(hasValidFactor) {
                $("#validFactorTableBody").html(validFactorRows); // 使用html()替换内容
                $("#validFactorList").removeClass('hide');
            }
        };

        let initReport = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                dataType: "json",
                contentType: "application/json",
                async: false,
                success: function (data) {
                    layer.closeAll();
                    let res = JSON.parse(data);
                    if (res.resultCode !== undefined) {
                        if (res.resultCode === 201) {
                            $("#report").hide();
                            $(".alert").removeClass("hide").addClass("show");
                            $(".alert").append('<img class="mr-1" src="/static/images/success.png" width="32" />您已经完成测试！');
                            return;
                        }
                    }
                    responseData = res.data;
                    record = res.data[0].testRecord;
                    scale = res.data[0].testRecord.scale;
                    user = res.data[0].testRecord.user;
                }
            });
        };
        let getBaseInfo = function () {
            $(".scaleName").html(scale.scaleName);
            $("#realName").html(user.realName === "" ? user.loginName : user.realName);
            $("#fullStructName").html(user.structFullName);
            $("#startDate").html(moment(record.startTime).format("YYYY-MM-DD"));
            $("#costTime").html(formatSeconds(record.timeInterval));
        };
        let getFactorInfo = function (res) {
            factors.splice(0);
            complexFactors.splice(0);
            complexScores.splice(0);
            originalFactors.splice(0);
            scores.splice(0);
            standartScores.splice(0);
            let singleFactorRows = "";
            let complexFactorRows = "";
            let validFactorRows = "";
            let hasSingleFactor = false;
            let hasComplexFactor = false;
            let hasValidFactor = false;

            $.each(res, function (index, content) {
                let factor = content.factor;

                // 检查是否为效度因子（测谎量表）
                if(factor.isLie === 1) {
                    hasValidFactor = true;
                    let explanation = getFactorExplanation(factor.id);
                    validFactorRows += "<tr><td class='text-center'>" + factor.factorName + "</td><td class='text-center'>" + toDecimal(content.score) + "</td><td class='text-center'>" + explanation + "</td></tr>";
                }

                if(factor.factorType === 1) {
                    // 普通因子
                    hasSingleFactor = true;
                    singleFactorRows += "<tr><td class='text-center'>" + factor.factorName + "</td><td class='text-center'>" + toDecimal(content.originalScore) + "</td><td class='text-center'>" + toDecimal(content.score) + "</td><td class='text-center'>"+factor.minScore+"~"+factor.maxScore+"</td></tr>";
                    if (res.length > 1 && factor.factorName !== '总分') {
                        factors.push(factor.factorName);
                        originalFactors.push(content.originalScore);
                        standartScores.push(content.abnormalValue);
                        scores.push(content.score);
                    }
                } else if(factor.factorType === 2 && factor.isLie !== 1) {
                    // 复合因子（排除效度因子）
                    hasComplexFactor = true;
                    isComplex = 1;
                    let explanation = getFactorExplanation(factor.id);
                    complexFactorRows += "<tr><td class='text-center'>" + factor.factorName + "</td><td class='text-center'>" + toDecimal(content.score) + "</td><td class='text-center'>" + explanation + "</td></tr>";
                    if (res.length > 1 && factor.factorName !== '总分') {
                        complexFactors.push(factor.factorName);
                        complexScores.push(content.score);
                    }
                }
            });

            if (scale.id == [[${apmAndNineHouse}]]) {
                factors.shift();
                scores.shift();
                standartScores.shift();
            }

            // 填充效度因子表格
            if(hasValidFactor) {
                $("#validFactorTableBody").append(validFactorRows);
                $("#validFactorList").removeClass('hide');
            } else {
                $("#validFactorList").addClass('hide');
            }

            // 填充普通因子表格
            if(hasSingleFactor) {
                $("#singleFactorTableBody").append(singleFactorRows);
                $("#singleFactorList").removeClass('hide');
            } else {
                $("#singleFactorList").addClass('hide');
            }

            // 填充复合因子表格
            if(hasComplexFactor) {
                $("#complexFactorTableBody").append(complexFactorRows);
                $("#complexFactorList").removeClass('hide');
            } else {
                $("#complexFactorList").addClass('hide');
            }
        };
        let generalReport = function (res) {
            if (scale.id == [[${pcm}]]) {
                $("#factorList").hide();
            }
            //因子得分情况
            getFactorInfo(res);
            creatCharts();
        };
        let sdsReport = function (res) {
            let listFactors = "";
            let originalScore = toDecimal(res[0].originalScore);
            let standardScore = toDecimal(res[0].score);
            let indexScore = toDecimal(res[0].originalScore / 80);
            let abnormalValue = toDecimal(res[0].abnormalValue);
            listFactors += "<tr>"
            listFactors += "<td class='text-center'>" + res[0].factor.factorName + "</td>";
            listFactors += "<td class='text-center'>" + originalScore + "</td>";
            listFactors += "<td class='text-center'>" + standardScore + "</td>";
            listFactors += "<td class='text-center'>" + indexScore + "</td>";
            listFactors += "<td class='text-center'>" + abnormalValue + "</td></tr>";
            $("#singleFactorTableBody").append(listFactors);
            $("#singleFactorList").removeClass('hide');
            $("#complexFactorList").addClass('hide');
            factors.push(res[0].factor.factorName);
            creatCharts();
        };
        let creatCharts = function(){
            // 分别处理普通因子和复合因子的图表
            createSingleFactorCharts();
            createComplexFactorCharts();
        };

        let createSingleFactorCharts = function() {
            // 获取普通因子图表配置
            let singleFactorCharts = scale.listCharts.filter(chart => chart.factorType === 1);

            // 如果没有配置普通因子图表，检查是否有旧的配置（没有factorType字段）
            if(singleFactorCharts.length === 0 && factors.length > 0) {
                // 检查是否有旧的图表配置（没有factorType字段的）
                let oldCharts = scale.listCharts.filter(chart => chart.factorType === undefined || chart.factorType === null);
                if(oldCharts.length > 0) {
                    // 使用旧的配置作为普通因子配置
                    singleFactorCharts = oldCharts.map(chart => ({...chart, factorType: 1}));
                } else {
                    // 使用默认配置
                    if(factors.length === 1) {
                        singleFactorCharts.push({factorType: 1, chartType: 'solidgauge'});
                    } else {
                        singleFactorCharts.push({factorType: 1, chartType: 'line'});
                        singleFactorCharts.push({factorType: 1, chartType: 'column'});
                    }
                }
            }

            $.each(singleFactorCharts, function(index, chartConfig) {
                // 深拷贝图表配置，避免数据污染
                let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));

                //抑郁自评量表特殊处理
                if (scale.id === [[${sds}]]){
                    chartOptions.series.push({ name: '抑郁指数值', "data": [responseData[0].originalScore / 80] });
                    chartOptions.title.text = '抑郁指数';
                    if(chartOptions.chart.type === 'gauge'){
                        chartOptions.yAxis.plotBands.push({from: 0, to: 0.49, color: '#55BF3B'});
                        chartOptions.yAxis.plotBands.push({from: 0.49, to: 0.6, color: '#DDDF0D'});
                        chartOptions.yAxis.plotBands.push({from: 0.6, to: 1, color: '#DF5353'});
                    }
                }
                else{
                    if(chartOptions.chart.type === 'gauge' || chartOptions.chart.type === 'solidgauge'){
                        // 对于速度仪图，显示第一个普通因子的数据
                        let singleFactorData = responseData.find(item => item.factor.factorType === 1);
                        if(singleFactorData) {
                            chartOptions.title.text = singleFactorData.factor.factorName;
                            chartOptions.yAxis.min = singleFactorData.factor.minScore;
                            chartOptions.yAxis.max = singleFactorData.factor.maxScore;
                            chartOptions.series.push({name: singleFactorData.factor.factorName, "data": [singleFactorData.score] });
                        }
                    }
                    else if(chartOptions.chart.type === 'pie') {
                        chartOptions.title.text = "普通因子得分占比情况";
                        let yData = [];
                        $.each(responseData.filter(item => item.factor.factorType === 1), function (index, content) {
                            yData.push({
                                name: content.factor.factorName, y: parseFloat((content.originalScore/90).toFixed(2))
                            })
                        });
                        chartOptions.series.push({
                            name: '因子分',
                            colorByPoint: true,
                            data:yData
                        });
                    }
                    else{
                        chartOptions.title.text = "普通因子得分统计图";
                        chartOptions.xAxis.categories = factors;
                        chartOptions.series.push({ name: '因子分', "data": scores,color: "#ffbc00" });
                        chartOptions.series.push({ name: '异常界值', "data": standartScores,color: "#fa5c7c" });
                    }
                }

                let canvasId = "#singleContainer" + (index + 1);
                let divChart = "#divSingleChart" + (index + 1);
                $(canvasId).removeClass('hide');
                $(divChart).removeClass('hide');
                chartOptions.chart.renderTo = 'singleContainer'+(index + 1);
                let chart1 = new Highcharts.Chart(chartOptions);
                let charData = $(canvasId).highcharts().getSVG();
                canvg('singleContainer'+(index + 1), charData);
                let chartsImg = $(canvasId)[0].toDataURL("image/png");
                chartsImgArray.push(chartsImg);
            });
        };

        let createComplexFactorCharts = function() {
            if(isComplex !== 1 || complexFactors.length === 0) {
                return;
            }

            // 获取复合因子图表配置
            let complexFactorCharts = scale.listCharts.filter(chart => chart.factorType === 2);

            // 如果没有配置复合因子图表，使用默认配置
            if(complexFactorCharts.length === 0) {
                complexFactorCharts.push({factorType: 2, chartType: 'bar'});
            }

            $.each(complexFactorCharts, function(index, chartConfig) {
                // 深拷贝图表配置，避免数据污染
                let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));

                if(chartOptions.chart.type === 'pie') {
                    chartOptions.title.text = "复合因子得分占比情况";
                    let yData = [];
                    $.each(responseData.filter(item => item.factor.factorType === 2), function (index, content) {
                        yData.push({
                            name: content.factor.factorName, y: parseFloat((content.score/100).toFixed(2))
                        })
                    });
                    chartOptions.series.push({
                        name: '因子分',
                        colorByPoint: true,
                        data:yData
                    });
                } else {
                    chartOptions.title.text = "复合因子得分统计图";
                    chartOptions.xAxis.categories = complexFactors;
                    chartOptions.series.push({ name: '复合因子分', "data": complexScores, color: "#28a745"});
                }

                let canvasId = "#complexContainer" + (index + 1);
                let divChart = "#divComplexChart" + (index + 1);
                $(canvasId).removeClass('hide');
                $(divChart).removeClass('hide');
                chartOptions.chart.renderTo = 'complexContainer'+(index + 1);
                let chart1 = new Highcharts.Chart(chartOptions);
                let charData = $(canvasId).highcharts().getSVG();
                canvg('complexContainer'+(index + 1), charData);
                let chartsImg = $(canvasId)[0].toDataURL("image/png");
                chartsImgArray.push(chartsImg);
            });
        };
        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.chartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {

                }
            });
        };
        let download = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.post("/export/test_report_word", { "recordId": recordId }, function (res) {
                layer.closeAll();
                if(res.resultCode ===200) {
                    location.href="/static/upload/"+res.resultMsg;
                }
                else {
                    layer.msg('下载失败!',{ icon: 2, time: 2000 });
                }
            }, 'json');
        };
        $(function () {
            initReport();
            //基本信息
            getBaseInfo();
            //检查并设置背景图片
            checkAndSetBackground();
            if (scale.id === [[${sds}]])//抑郁自评量表
                sdsReport(responseData);
            else
                generalReport(responseData);

            //获取因子结果解释
            getFactorExplains(recordId);
            if (getUrlParam("savecharts") === 'true') {
                saveCharts();
            }
        });

        // 获取因子结果解释
        let getFactorExplains = function(recordId) {
            $.ajax({
                url: '/measuringroom/testing/getFactorExplains?recordId='+recordId,
                type: 'GET',
                data: '',
                dataType: 'json',
                success: function(result) {
                    console.log('获取因子解释结果:', result);
                    if(result.resultCode === 200 && result.data && result.data.length > 0) {
                        // 存储因子解释数据
                        factorExplainsData = result.data;

                        let complexFactorExplains = "";
                        let singleFactorExplains = "";
                        let validFactorExplains = "";
                        let hasComplexExplain = false;
                        let hasSingleExplain = false;
                        let hasValidExplain = false;

                        // 根据因子类型分组解释
                        $.each(result.data, function(index, explain) {
                            if(explain.factorId === 0) {
                                // factorId为0的是总体解释，已移除总体解释区域，跳过
                                return true;
                            }

                            // 根据factorId查找对应的因子信息
                            let factorInfo = null;
                            $.each(responseData, function(i, data) {
                                if(data.factor.id === explain.factorId) {
                                    factorInfo = data.factor;
                                    return false;
                                }
                            });
                            console.log('找到的因子信息:', factorInfo);

                            if(factorInfo) {
                                if(factorInfo.isLie === 1) {
                                    // 效度因子解释 - 更新效度因子表格
                                    hasValidExplain = true;
                                    updateValidFactorTable();
                                } else if(factorInfo.factorType === 2) {
                                    // 复合因子解释
                                    hasComplexExplain = true;
                                    complexFactorExplains += "<dt class='pb10'>" + factorInfo.factorName + "</dt>";
                                    complexFactorExplains += "<dd class='pb10'>" + explain.interpretation + "</dd>";
                                } else {
                                    // 普通因子解释
                                    hasSingleExplain = true;
                                    singleFactorExplains += "<dt class='pb10'>" + factorInfo.factorName + "</dt>";
                                    singleFactorExplains += "<dd class='pb10'>" + explain.interpretation + "</dd>";
                                }
                            }
                        });

                        // 显示复合因子解释
                        if(hasComplexExplain) {
                            $("#complexFactorExplain dl").html(complexFactorExplains);
                            $("#complexFactorExplainSection").removeClass('hide');
                        } else {
                            $("#complexFactorExplainSection").addClass('hide');
                        }

                        // 显示普通因子解释
                        if(hasSingleExplain) {
                            $("#singleFactorExplain dl").html(singleFactorExplains);
                            $("#singleFactorExplainSection").removeClass('hide');
                        } else {
                            $("#singleFactorExplainSection").addClass('hide');
                        }
                    }
                }
            });
        };

        // 检查并设置背景图片
        function checkAndSetBackground() {
            $.ajax({
                url: "/measuringroom/testing/get_background",
                type: 'POST',
                data: { recordId: recordId },
                dataType: "JSON",
                success: function (res) {
                    if (res.resultCode === 200 && res.data && res.data.showBackground && res.data.backgroundUrl) {
                        // 显示背景图片版本
                        $("#reportHeaderBg").css("background-image", "url('" + res.data.backgroundUrl + "')").show();
                        $("#normalHeader").hide();
                    } else {
                        // 显示普通版本
                        $("#reportHeaderBg").hide();
                        $("#normalHeader").show();
                    }
                },
                error: function () {
                    // 出错时显示普通版本
                    $("#reportHeaderBg").hide();
                    $("#normalHeader").show();
                }
            });
        }
    </script>
</th:block>
</body>
</html>